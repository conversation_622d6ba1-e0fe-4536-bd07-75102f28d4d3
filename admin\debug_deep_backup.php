<?php
// admin/debug_deep_backup.php
// Debug approfondito per identificare il problema del backup

session_start();
$_SESSION['user_id'] = 1;

ini_set('display_errors', 1);
error_reporting(E_ALL);

echo "<h2>🔬 Debug Approfondito Backup</h2>";

// 1. Verifica configurazione PHP
echo "<h3>⚙️ Configurazione PHP</h3>";
echo "<p><strong>Versione PHP:</strong> " . phpversion() . "</p>";
echo "<p><strong>Sistema Operativo:</strong> " . php_uname() . "</p>";
echo "<p><strong>ZipArchive disponibile:</strong> " . (class_exists('ZipArchive') ? '✅ Sì' : '❌ No') . "</p>";
echo "<p><strong>Directory temporanea:</strong> " . htmlspecialchars(sys_get_temp_dir()) . "</p>";
echo "<p><strong>Directory separatore:</strong> " . htmlspecialchars(DIRECTORY_SEPARATOR) . "</p>";

// 2. Test percorsi
echo "<h3>📁 Test Percorsi</h3>";
$temp_dir = sys_get_temp_dir();
$test_dir = $temp_dir . DIRECTORY_SEPARATOR . 'debug_test_' . time();

echo "<p><strong>Percorso temp originale:</strong> " . htmlspecialchars($temp_dir) . "</p>";
echo "<p><strong>Percorso test directory:</strong> " . htmlspecialchars($test_dir) . "</p>";
echo "<p><strong>Percorso normalizzato:</strong> " . htmlspecialchars(str_replace('\\', '/', $test_dir)) . "</p>";

// 3. Test creazione directory
if (mkdir($test_dir, 0777, true)) {
    echo "<p>✅ Directory creata con successo</p>";
    
    // Crea file di test
    $test_file = $test_dir . DIRECTORY_SEPARATOR . 'test.txt';
    file_put_contents($test_file, 'Test content');
    echo "<p>✅ File di test creato: " . htmlspecialchars($test_file) . "</p>";
    
    // 4. Test ZipArchive base
    echo "<h3>📦 Test ZipArchive Base</h3>";
    $zip_file = $temp_dir . DIRECTORY_SEPARATOR . 'debug_test_' . time() . '.zip';
    echo "<p><strong>File ZIP:</strong> " . htmlspecialchars($zip_file) . "</p>";
    
    $zip = new ZipArchive();
    $result = $zip->open($zip_file, ZipArchive::CREATE);
    
    if ($result === TRUE) {
        echo "<p>✅ ZIP aperto correttamente</p>";
        
        // Test 1: Aggiungi file con percorso assoluto
        $add_result1 = $zip->addFile($test_file, 'test_absolute.txt');
        echo "<p><strong>Aggiunta file assoluto:</strong> " . ($add_result1 ? '✅ Successo' : '❌ Fallito') . "</p>";
        
        // Test 2: Aggiungi file con percorso relativo
        $add_result2 = $zip->addFile($test_file, 'folder/test_relative.txt');
        echo "<p><strong>Aggiunta file relativo:</strong> " . ($add_result2 ? '✅ Successo' : '❌ Fallito') . "</p>";
        
        // Test 3: Aggiungi da stringa
        $add_result3 = $zip->addFromString('test_string.txt', 'Contenuto da stringa');
        echo "<p><strong>Aggiunta da stringa:</strong> " . ($add_result3 ? '✅ Successo' : '❌ Fallito') . "</p>";
        
        $zip->close();
        echo "<p>✅ ZIP chiuso</p>";
        
        // 5. Verifica contenuto ZIP
        echo "<h3>🔍 Verifica Contenuto ZIP</h3>";
        $zip_read = new ZipArchive();
        if ($zip_read->open($zip_file) === TRUE) {
            echo "<p><strong>Numero file nel ZIP:</strong> " . $zip_read->numFiles . "</p>";
            echo "<ul>";
            
            for ($i = 0; $i < $zip_read->numFiles; $i++) {
                $file_info = $zip_read->statIndex($i);
                $filename = $file_info['name'];
                echo "<li>" . htmlspecialchars($filename) . "</li>";
            }
            echo "</ul>";
            $zip_read->close();
        } else {
            echo "<p>❌ Impossibile leggere il ZIP</p>";
        }
        
        // Pulisci
        unlink($zip_file);
        
    } else {
        echo "<p>❌ Errore apertura ZIP: " . $result . "</p>";
        
        // Decodifica errore
        $errors = [
            ZipArchive::ER_OK => 'Nessun errore',
            ZipArchive::ER_MULTIDISK => 'Multi-disk zip archives not supported',
            ZipArchive::ER_RENAME => 'Renaming temporary file failed',
            ZipArchive::ER_CLOSE => 'Closing zip archive failed',
            ZipArchive::ER_SEEK => 'Seek error',
            ZipArchive::ER_READ => 'Read error',
            ZipArchive::ER_WRITE => 'Write error',
            ZipArchive::ER_CRC => 'CRC error',
            ZipArchive::ER_ZIPCLOSED => 'Containing zip archive was closed',
            ZipArchive::ER_NOENT => 'No such file',
            ZipArchive::ER_EXISTS => 'File already exists',
            ZipArchive::ER_OPEN => 'Can\'t open file',
            ZipArchive::ER_TMPOPEN => 'Failure to create temporary file',
            ZipArchive::ER_ZLIB => 'Zlib error',
            ZipArchive::ER_MEMORY => 'Memory allocation failure',
            ZipArchive::ER_CHANGED => 'Entry has been changed',
            ZipArchive::ER_COMPNOTSUPP => 'Compression method not supported',
            ZipArchive::ER_EOF => 'Premature EOF',
            ZipArchive::ER_INVAL => 'Invalid argument',
            ZipArchive::ER_NOZIP => 'Not a zip archive',
            ZipArchive::ER_INTERNAL => 'Internal error',
            ZipArchive::ER_INCONS => 'Zip archive inconsistent',
            ZipArchive::ER_REMOVE => 'Can\'t remove file',
            ZipArchive::ER_DELETED => 'Entry has been deleted'
        ];
        
        $error_msg = isset($errors[$result]) ? $errors[$result] : 'Errore sconosciuto';
        echo "<p><strong>Dettaglio errore:</strong> " . htmlspecialchars($error_msg) . "</p>";
    }
    
    // Pulisci directory test
    unlink($test_file);
    rmdir($test_dir);
    
} else {
    echo "<p>❌ Impossibile creare directory di test</p>";
}

// 6. Test permessi
echo "<h3>🔐 Test Permessi</h3>";
echo "<p><strong>Directory temp scrivibile:</strong> " . (is_writable($temp_dir) ? '✅ Sì' : '❌ No') . "</p>";
echo "<p><strong>Directory temp leggibile:</strong> " . (is_readable($temp_dir) ? '✅ Sì' : '❌ No') . "</p>";

// 7. Test con percorso semplificato
echo "<h3>🧪 Test Percorso Semplificato</h3>";
$simple_dir = 'C:' . DIRECTORY_SEPARATOR . 'temp_asdp_test';
if (!is_dir($simple_dir)) {
    if (mkdir($simple_dir, 0777, true)) {
        echo "<p>✅ Directory semplificata creata: " . htmlspecialchars($simple_dir) . "</p>";
        
        $simple_file = $simple_dir . DIRECTORY_SEPARATOR . 'test.txt';
        file_put_contents($simple_file, 'Test semplificato');
        
        $simple_zip = $simple_dir . DIRECTORY_SEPARATOR . 'test.zip';
        $zip_simple = new ZipArchive();
        
        if ($zip_simple->open($simple_zip, ZipArchive::CREATE) === TRUE) {
            $zip_simple->addFile($simple_file, 'test.txt');
            $zip_simple->close();
            
            echo "<p>✅ ZIP semplificato creato</p>";
            
            // Verifica
            $zip_verify = new ZipArchive();
            if ($zip_verify->open($simple_zip) === TRUE) {
                echo "<p><strong>File nel ZIP semplificato:</strong></p><ul>";
                for ($i = 0; $i < $zip_verify->numFiles; $i++) {
                    $file_info = $zip_verify->statIndex($i);
                    echo "<li>" . htmlspecialchars($file_info['name']) . "</li>";
                }
                echo "</ul>";
                $zip_verify->close();
            }
            
            // Pulisci
            unlink($simple_zip);
        } else {
            echo "<p>❌ Errore creazione ZIP semplificato</p>";
        }
        
        unlink($simple_file);
        rmdir($simple_dir);
    } else {
        echo "<p>❌ Impossibile creare directory semplificata</p>";
    }
}

echo "<hr>";
echo "<h3>💡 Raccomandazioni</h3>";
echo "<ul>";
echo "<li>Se tutti i test base funzionano, il problema è nella logica del backup</li>";
echo "<li>Se i test base falliscono, il problema è nella configurazione PHP/XAMPP</li>";
echo "<li>Se solo il percorso semplificato funziona, il problema è nei percorsi complessi di Windows</li>";
echo "</ul>";

echo "<p><a href='test_backup_comparison.php'>← Torna al confronto</a></p>";
?>
