<?php
// admin/backup_process_v3.php
// Versione alternativa usando approccio Windows nativo

session_start();

ini_set('display_errors', 1);
error_reporting(E_ALL);

if (!isset($_SESSION['user_id'])) {
    header('Content-Type: application/json');
    echo json_encode(['success' => false, 'error' => 'Utente non autenticato']);
    exit;
}

require_once '../includes/db_config.php';
require_once '../includes/Logger.php';

$logger = Logger::getInstance();

function findMysqldump() {
    $paths = [
        'C:\\xampp\\mysql\\bin\\mysqldump.exe',
        'C:\\wamp64\\bin\\mysql\\mysql8.0.31\\bin\\mysqldump.exe',
        'C:\\wamp\\bin\\mysql\\mysql5.7.36\\bin\\mysqldump.exe'
    ];

    foreach ($paths as $path) {
        if (file_exists($path)) {
            return $path;
        }
    }
    return false;
}

function performWindowsNativeBackup() {
    global $logger;
    
    try {
        $logger->info('Avvio backup Windows nativo');
        
        // Usa una directory più semplice
        $backup_base = 'C:' . DIRECTORY_SEPARATOR . 'asdp_backup_' . date('Y-m-d_H-i-s');
        
        if (!mkdir($backup_base, 0777, true)) {
            throw new Exception("Impossibile creare directory backup: " . $backup_base);
        }
        
        $logger->info('Directory backup creata', ['dir' => $backup_base]);
        
        // Crea sottodirectory
        mkdir($backup_base . DIRECTORY_SEPARATOR . 'database');
        mkdir($backup_base . DIRECTORY_SEPARATOR . 'files');
        
        // 1. Backup database
        $mysqldump_path = findMysqldump();
        if (!$mysqldump_path) {
            throw new Exception("mysqldump non trovato");
        }
        
        $db_file = $backup_base . DIRECTORY_SEPARATOR . 'database' . DIRECTORY_SEPARATOR . DB_NAME . '.sql';
        $command = sprintf(
            '"%s" --host=%s --user=%s --password=%s --databases %s > "%s" 2>&1',
            $mysqldump_path,
            escapeshellarg(DB_HOST),
            escapeshellarg(DB_USER),
            escapeshellarg(DB_PASS),
            escapeshellarg(DB_NAME),
            $db_file
        );
        
        exec($command, $output, $return_var);
        if ($return_var !== 0) {
            throw new Exception("Errore backup database: " . implode("\n", $output));
        }
        
        $logger->info('Database backup completato');
        
        // 2. Copia file applicazione
        $base_dir = dirname(__DIR__);
        $excluded_patterns = ['vendor/*', 'node_modules/*', '.git/*', 'temp/*', 'tmp/*', 'backups/*'];
        
        $iterator = new RecursiveIteratorIterator(
            new RecursiveDirectoryIterator($base_dir),
            RecursiveIteratorIterator::SELF_FIRST
        );
        
        $files_copied = 0;
        foreach ($iterator as $file) {
            if ($file->isFile()) {
                $file_path = $file->getRealPath();
                $relative_path = substr($file_path, strlen($base_dir) + 1);
                $relative_path = str_replace('\\', '/', $relative_path);
                
                // Controlla esclusioni
                $exclude = false;
                foreach ($excluded_patterns as $pattern) {
                    if (fnmatch($pattern, $relative_path)) {
                        $exclude = true;
                        break;
                    }
                }
                
                if (!$exclude) {
                    $dest_path = $backup_base . DIRECTORY_SEPARATOR . 'files' . DIRECTORY_SEPARATOR . str_replace('/', DIRECTORY_SEPARATOR, $relative_path);
                    $dest_dir = dirname($dest_path);
                    
                    if (!is_dir($dest_dir)) {
                        mkdir($dest_dir, 0777, true);
                    }
                    
                    if (copy($file_path, $dest_path)) {
                        $files_copied++;
                    }
                }
            }
        }
        
        $logger->info('File copiati', ['count' => $files_copied]);
        
        // 3. Genera report
        $report_content = "ASDP - Report Backup Windows Nativo\n";
        $report_content .= "===================================\n\n";
        $report_content .= "Data backup: " . date('d/m/Y H:i:s') . "\n";
        $report_content .= "Utente: " . $_SESSION['nome'] . " " . $_SESSION['cognome'] . "\n";
        $report_content .= "File copiati: " . $files_copied . "\n";
        $report_content .= "Directory backup: " . $backup_base . "\n";
        $report_content .= "Metodo: Windows nativo con PowerShell\n";
        
        file_put_contents($backup_base . DIRECTORY_SEPARATOR . 'backup_report.txt', $report_content);
        
        // 4. Crea ZIP usando PowerShell (metodo Windows nativo)
        $zip_file = 'C:' . DIRECTORY_SEPARATOR . 'asdp_backup_native_' . date('Y-m-d_H-i-s') . '.zip';
        
        // Comando PowerShell per creare ZIP
        $powershell_command = sprintf(
            'powershell -Command "Compress-Archive -Path \'%s\\*\' -DestinationPath \'%s\' -Force"',
            $backup_base,
            $zip_file
        );
        
        $logger->debug('Comando PowerShell', ['command' => $powershell_command]);
        
        exec($powershell_command, $ps_output, $ps_return);
        
        if ($ps_return !== 0) {
            $logger->error('Errore PowerShell', ['output' => $ps_output]);
            
            // Fallback: usa ZipArchive con logica semplificata
            $logger->info('Fallback a ZipArchive');
            
            $zip_file = 'C:' . DIRECTORY_SEPARATOR . 'asdp_backup_fallback_' . date('Y-m-d_H-i-s') . '.zip';
            $zip = new ZipArchive();
            
            if ($zip->open($zip_file, ZipArchive::CREATE) === TRUE) {
                
                // Aggiungi report
                $zip->addFile($backup_base . DIRECTORY_SEPARATOR . 'backup_report.txt', 'backup_report.txt');
                
                // Aggiungi database
                $zip->addFile($db_file, 'database/' . DB_NAME . '.sql');
                
                // Aggiungi file applicazione (solo i principali per evitare problemi)
                $main_files = [
                    'index.php',
                    'home.php',
                    'calcolo_sismico.php'
                ];
                
                foreach ($main_files as $main_file) {
                    $source_file = $backup_base . DIRECTORY_SEPARATOR . 'files' . DIRECTORY_SEPARATOR . $main_file;
                    if (file_exists($source_file)) {
                        $zip->addFile($source_file, 'files/' . $main_file);
                    }
                }
                
                // Aggiungi directory admin
                $admin_dir = $backup_base . DIRECTORY_SEPARATOR . 'files' . DIRECTORY_SEPARATOR . 'admin';
                if (is_dir($admin_dir)) {
                    $admin_iterator = new RecursiveIteratorIterator(
                        new RecursiveDirectoryIterator($admin_dir, RecursiveDirectoryIterator::SKIP_DOTS)
                    );
                    
                    foreach ($admin_iterator as $admin_file) {
                        if ($admin_file->isFile()) {
                            $admin_path = $admin_file->getRealPath();
                            $admin_relative = 'files/admin/' . substr($admin_path, strlen($admin_dir) + 1);
                            $admin_relative = str_replace('\\', '/', $admin_relative);
                            $zip->addFile($admin_path, $admin_relative);
                        }
                    }
                }
                
                $zip->close();
                $logger->info('ZIP fallback creato');
            } else {
                throw new Exception("Impossibile creare ZIP anche con fallback");
            }
        } else {
            $logger->info('ZIP PowerShell creato con successo');
        }
        
        // Pulisci directory temporanea
        $cleanup_iterator = new RecursiveIteratorIterator(
            new RecursiveDirectoryIterator($backup_base, RecursiveDirectoryIterator::SKIP_DOTS),
            RecursiveIteratorIterator::CHILD_FIRST
        );
        
        foreach ($cleanup_iterator as $cleanup_file) {
            if ($cleanup_file->isDir()) {
                rmdir($cleanup_file->getRealPath());
            } else {
                unlink($cleanup_file->getRealPath());
            }
        }
        rmdir($backup_base);
        
        // Sposta il ZIP nella directory temporanea standard per il download
        $final_zip = sys_get_temp_dir() . DIRECTORY_SEPARATOR . basename($zip_file);
        if (file_exists($zip_file)) {
            rename($zip_file, $final_zip);
        }
        
        return [
            'success' => true,
            'message' => 'Backup Windows nativo completato',
            'downloadUrl' => 'download_backup.php?file=' . basename($final_zip),
            'method' => 'windows_native',
            'files_copied' => $files_copied
        ];
        
    } catch (Exception $e) {
        $logger->error('Errore backup Windows nativo', ['error' => $e->getMessage()]);
        
        // Pulisci in caso di errore
        if (isset($backup_base) && is_dir($backup_base)) {
            $cleanup_iterator = new RecursiveIteratorIterator(
                new RecursiveDirectoryIterator($backup_base, RecursiveDirectoryIterator::SKIP_DOTS),
                RecursiveIteratorIterator::CHILD_FIRST
            );
            foreach ($cleanup_iterator as $cleanup_file) {
                if ($cleanup_file->isDir()) {
                    rmdir($cleanup_file->getRealPath());
                } else {
                    unlink($cleanup_file->getRealPath());
                }
            }
            rmdir($backup_base);
        }
        
        throw $e;
    }
}

// Esegui il backup
try {
    header('Content-Type: application/json');
    $result = performWindowsNativeBackup();
    echo json_encode($result);
} catch (Exception $e) {
    header('Content-Type: application/json');
    echo json_encode([
        'success' => false,
        'error' => $e->getMessage()
    ]);
}
?>
