<?php
session_start();
require_once '../includes/Logger.php';

// Verifica se l'utente è autenticato
if (!isset($_SESSION['user_id'])) {
    die('Accesso non autorizzato');
}

$logger = Logger::getInstance();

// Verifica se è stato specificato un file
if (!isset($_GET['file'])) {
    $logger->error('Tentativo di download senza specificare il file');
    die('File non specificato');
}

// Pulisci il nome del file
$filename = basename($_GET['file']);
$filepath = sys_get_temp_dir() . DIRECTORY_SEPARATOR . $filename;

// Verifica che il file esista e sia un backup valido
// Accetta anche file di test per debug
$valid_patterns = [
    '/^asdp_backup_\d{4}-\d{2}-\d{2}_\d{2}-\d{2}-\d{2}\.zip$/',
    '/^test_.*\.zip$/',
    '/^debug_.*\.zip$/'
];

$is_valid = false;
foreach ($valid_patterns as $pattern) {
    if (preg_match($pattern, $filename)) {
        $is_valid = true;
        break;
    }
}

if (!file_exists($filepath) || !$is_valid) {
    $logger->error('Tentativo di download di un file non valido', ['file' => $filename, 'exists' => file_exists($filepath)]);
    die('File non valido o non esistente: ' . htmlspecialchars($filename));
}

// Log del download
$logger->info('Download backup', ['file' => $filename, 'user' => $_SESSION['user_id']]);

// Invia il file
header('Content-Type: application/zip');
header('Content-Disposition: attachment; filename="' . $filename . '"');
header('Content-Length: ' . filesize($filepath));
header('Cache-Control: no-cache');

readfile($filepath);

// Elimina il file temporaneo dopo il download
unlink($filepath);
?>
