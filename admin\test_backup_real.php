<?php
// admin/test_backup_real.php
// Test del backup reale con debug dettagliato

session_start();
$_SESSION['user_id'] = 1;
$_SESSION['nome'] = 'Test';
$_SESSION['cognome'] = 'User';

ini_set('display_errors', 1);
error_reporting(E_ALL);

echo "<h2>🔧 Test Backup Reale con Debug</h2>";

// Simula il backup reale ma con output di debug
require_once '../includes/db_config.php';
require_once '../includes/Logger.php';

$logger = Logger::getInstance();

function testRealBackup() {
    global $logger;
    
    try {
        echo "<h3>🚀 Avvio test backup reale</h3>";
        
        // Crea directory temporanea
        $backup_dir = sys_get_temp_dir() . DIRECTORY_SEPARATOR . 'test_real_backup_' . date('Y-m-d_H-i-s');
        echo "<p><strong>Directory backup:</strong> " . htmlspecialchars($backup_dir) . "</p>";
        
        if (!mkdir($backup_dir, 0777, true)) {
            throw new Exception("Impossibile creare directory backup");
        }
        
        mkdir($backup_dir . DIRECTORY_SEPARATOR . 'database');
        mkdir($backup_dir . DIRECTORY_SEPARATOR . 'files');
        echo "<p>✅ Directory create</p>";
        
        // Simula backup database (senza mysqldump per semplicità)
        $db_content = "-- Test Database Backup\nCREATE TABLE users (id INT, name VARCHAR(255));\nINSERT INTO users VALUES (1, 'Test User');";
        file_put_contents($backup_dir . DIRECTORY_SEPARATOR . 'database' . DIRECTORY_SEPARATOR . 'asdp.sql', $db_content);
        echo "<p>✅ Database simulato creato</p>";
        
        // Copia alcuni file reali dell'applicazione
        $base_dir = dirname(__DIR__);
        $files_to_copy = [
            'index.php',
            'home.php',
            'admin' . DIRECTORY_SEPARATOR . 'backup.php'
        ];
        
        $files_copied = 0;
        foreach ($files_to_copy as $file) {
            $source = $base_dir . DIRECTORY_SEPARATOR . $file;
            if (file_exists($source)) {
                $dest = $backup_dir . DIRECTORY_SEPARATOR . 'files' . DIRECTORY_SEPARATOR . $file;
                $dest_dir = dirname($dest);
                if (!is_dir($dest_dir)) {
                    mkdir($dest_dir, 0777, true);
                }
                if (copy($source, $dest)) {
                    $files_copied++;
                    echo "<p>📄 Copiato: " . htmlspecialchars($file) . "</p>";
                }
            }
        }
        
        // Genera report
        $report = "Test Backup Report\n";
        $report .= "==================\n";
        $report .= "Data: " . date('d/m/Y H:i:s') . "\n";
        $report .= "File copiati: " . $files_copied . "\n";
        file_put_contents($backup_dir . DIRECTORY_SEPARATOR . 'backup_report.txt', $report);
        echo "<p>✅ Report generato</p>";
        
        // Crea ZIP con metodo semplificato
        $zip_file = sys_get_temp_dir() . DIRECTORY_SEPARATOR . 'test_real_backup_' . date('Y-m-d_H-i-s') . '.zip';
        echo "<p><strong>File ZIP:</strong> " . htmlspecialchars($zip_file) . "</p>";
        
        $zip = new ZipArchive();
        $open_result = $zip->open($zip_file, ZipArchive::CREATE);
        
        if ($open_result !== TRUE) {
            throw new Exception("Errore apertura ZIP: " . $open_result);
        }
        
        echo "<p>✅ ZIP aperto per scrittura</p>";
        
        // Metodo semplificato: aggiungi file uno per uno
        echo "<h4>📦 Aggiunta file al ZIP:</h4>";
        
        // 1. Report
        $report_file = $backup_dir . DIRECTORY_SEPARATOR . 'backup_report.txt';
        if ($zip->addFile($report_file, 'backup_report.txt')) {
            echo "<p>✅ Report aggiunto</p>";
        } else {
            echo "<p>❌ Errore aggiunta report</p>";
        }
        
        // 2. Database
        $db_file = $backup_dir . DIRECTORY_SEPARATOR . 'database' . DIRECTORY_SEPARATOR . 'asdp.sql';
        if ($zip->addFile($db_file, 'database/asdp.sql')) {
            echo "<p>✅ Database aggiunto</p>";
        } else {
            echo "<p>❌ Errore aggiunta database</p>";
        }
        
        // 3. File applicazione
        foreach ($files_to_copy as $file) {
            $source_file = $backup_dir . DIRECTORY_SEPARATOR . 'files' . DIRECTORY_SEPARATOR . $file;
            if (file_exists($source_file)) {
                $zip_path = 'files/' . str_replace(DIRECTORY_SEPARATOR, '/', $file);
                if ($zip->addFile($source_file, $zip_path)) {
                    echo "<p>✅ File aggiunto: " . htmlspecialchars($zip_path) . "</p>";
                } else {
                    echo "<p>❌ Errore aggiunta: " . htmlspecialchars($zip_path) . "</p>";
                }
            }
        }
        
        // Chiudi ZIP
        if ($zip->close()) {
            echo "<p>✅ ZIP chiuso correttamente</p>";
        } else {
            echo "<p>❌ Errore chiusura ZIP</p>";
        }
        
        // Verifica ZIP
        echo "<h4>🔍 Verifica ZIP:</h4>";
        echo "<p><strong>Dimensione:</strong> " . filesize($zip_file) . " bytes</p>";
        
        $zip_read = new ZipArchive();
        if ($zip_read->open($zip_file) === TRUE) {
            echo "<p><strong>File nel ZIP:</strong> " . $zip_read->numFiles . "</p>";
            echo "<ul>";
            
            $has_problems = false;
            for ($i = 0; $i < $zip_read->numFiles; $i++) {
                $file_info = $zip_read->statIndex($i);
                $filename = $file_info['name'];
                
                if (strpos($filename, 'temp_') !== false || strpos($filename, '\\') !== false || preg_match('/^[A-Z]:/', $filename)) {
                    $has_problems = true;
                    echo "<li style='color: red;'>❌ " . htmlspecialchars($filename) . " (PROBLEMA!)</li>";
                } else {
                    echo "<li style='color: green;'>✅ " . htmlspecialchars($filename) . "</li>";
                }
            }
            echo "</ul>";
            
            if (!$has_problems) {
                echo "<p style='color: green; font-weight: bold;'>🎉 STRUTTURA ZIP CORRETTA!</p>";
            } else {
                echo "<p style='color: red; font-weight: bold;'>❌ Problemi nella struttura ZIP</p>";
            }
            
            $zip_read->close();
            
            // Link download
            echo "<p><a href='download_backup.php?file=" . basename($zip_file) . "' target='_blank' style='background: #007bff; color: white; padding: 10px; text-decoration: none; border-radius: 5px;'>📥 Scarica ZIP di test</a></p>";
            
        } else {
            echo "<p>❌ Impossibile leggere il ZIP creato</p>";
        }
        
        // Pulisci directory temporanea
        $cleanup_iterator = new RecursiveIteratorIterator(
            new RecursiveDirectoryIterator($backup_dir, RecursiveDirectoryIterator::SKIP_DOTS),
            RecursiveIteratorIterator::CHILD_FIRST
        );
        
        foreach ($cleanup_iterator as $cleanup_file) {
            if ($cleanup_file->isDir()) {
                rmdir($cleanup_file->getRealPath());
            } else {
                unlink($cleanup_file->getRealPath());
            }
        }
        rmdir($backup_dir);
        
        echo "<p>🧹 Directory temporanea pulita</p>";
        
    } catch (Exception $e) {
        echo "<p style='color: red;'>❌ Errore: " . htmlspecialchars($e->getMessage()) . "</p>";
    }
}

// Esegui il test
testRealBackup();

echo "<hr>";
echo "<p><a href='backup.php'>← Torna al backup</a></p>";
?>
