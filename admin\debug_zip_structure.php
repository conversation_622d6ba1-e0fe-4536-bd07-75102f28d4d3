<?php
// admin/debug_zip_structure.php
// Debug specifico per la struttura ZIP

session_start();
$_SESSION['user_id'] = 1;

ini_set('display_errors', 1);
error_reporting(E_ALL);

echo "<h2>🔍 Debug Struttura ZIP Specifica</h2>";

// Simula esattamente quello che fa il backup
$backup_dir = sys_get_temp_dir() . DIRECTORY_SEPARATOR . 'debug_backup_' . time();
echo "<p><strong>Directory backup:</strong> " . htmlspecialchars($backup_dir) . "</p>";

// Crea la struttura esatta del backup
mkdir($backup_dir, 0777, true);
mkdir($backup_dir . DIRECTORY_SEPARATOR . 'database');
mkdir($backup_dir . DIRECTORY_SEPARATOR . 'files');

// File di test
file_put_contents($backup_dir . DIRECTORY_SEPARATOR . 'backup_report.txt', 'Test Report');
file_put_contents($backup_dir . DIRECTORY_SEPARATOR . 'database' . DIRECTORY_SEPARATOR . 'test.sql', 'CREATE TABLE test;');
file_put_contents($backup_dir . DIRECTORY_SEPARATOR . 'files' . DIRECTORY_SEPARATOR . 'index.php', '<?php echo "test"; ?>');

echo "<p>✅ Struttura creata</p>";

// Crea ZIP con la logica ESATTA del backup
$zip_file = sys_get_temp_dir() . DIRECTORY_SEPARATOR . 'debug_backup_' . time() . '.zip';
$zip = new ZipArchive();

if ($zip->open($zip_file, ZipArchive::CREATE) === TRUE) {
    echo "<p>✅ ZIP aperto</p>";
    
    // Normalizza il percorso della directory di backup per Windows
    $backup_dir_normalized = str_replace('\\', '/', $backup_dir);
    echo "<p><strong>Backup dir normalizzato:</strong> " . htmlspecialchars($backup_dir_normalized) . "</p>";
    
    // Aggiungi il report nella root del ZIP
    $report_file = $backup_dir . DIRECTORY_SEPARATOR . 'backup_report.txt';
    if (file_exists($report_file)) {
        $result = $zip->addFile($report_file, 'backup_report.txt');
        echo "<p>Report aggiunto: " . ($result ? '✅' : '❌') . "</p>";
    }

    // Funzione helper per aggiungere file ricorsivamente
    $addDirectoryToZip = function($source_dir, $zip_prefix) use ($zip, $backup_dir_normalized) {
        echo "<h4>📁 Aggiunta directory: " . htmlspecialchars($source_dir) . " → " . htmlspecialchars($zip_prefix) . "</h4>";
        
        if (!is_dir($source_dir)) {
            echo "<p>❌ Directory non esiste</p>";
            return;
        }
        
        $source_dir_normalized = str_replace('\\', '/', $source_dir);
        echo "<p><strong>Source dir normalizzato:</strong> " . htmlspecialchars($source_dir_normalized) . "</p>";
        
        $iterator = new RecursiveIteratorIterator(
            new RecursiveDirectoryIterator($source_dir, RecursiveDirectoryIterator::SKIP_DOTS),
            RecursiveIteratorIterator::SELF_FIRST
        );
        
        foreach ($iterator as $file) {
            if ($file->isFile()) {
                $file_path = $file->getRealPath();
                $file_path_normalized = str_replace('\\', '/', $file_path);
                
                echo "<p><strong>File trovato:</strong> " . htmlspecialchars($file_path) . "</p>";
                echo "<p><strong>File normalizzato:</strong> " . htmlspecialchars($file_path_normalized) . "</p>";
                
                // Calcola il percorso relativo dal source_dir
                $relative_from_source = substr($file_path_normalized, strlen($source_dir_normalized) + 1);
                echo "<p><strong>Relativo da source:</strong> " . htmlspecialchars($relative_from_source) . "</p>";
                
                $zip_path = $zip_prefix . '/' . $relative_from_source;
                echo "<p><strong>Percorso ZIP finale:</strong> " . htmlspecialchars($zip_path) . "</p>";
                
                $result = $zip->addFile($file_path, $zip_path);
                echo "<p>Aggiunta: " . ($result ? '✅' : '❌') . "</p>";
                echo "<hr>";
            }
        }
    };

    // Aggiungi i file del database
    $db_dir = $backup_dir . DIRECTORY_SEPARATOR . 'database';
    $addDirectoryToZip($db_dir, 'database');

    // Aggiungi i file dell'applicazione
    $files_dir = $backup_dir . DIRECTORY_SEPARATOR . 'files';
    $addDirectoryToZip($files_dir, 'files');
    
    $zip->close();
    echo "<p>✅ ZIP chiuso</p>";
    
    // Verifica contenuto ZIP
    echo "<h3>🔍 Contenuto ZIP Finale:</h3>";
    $zip_read = new ZipArchive();
    if ($zip_read->open($zip_file) === TRUE) {
        echo "<ul>";
        for ($i = 0; $i < $zip_read->numFiles; $i++) {
            $file_info = $zip_read->statIndex($i);
            $filename = $file_info['name'];
            
            // Controlla se ci sono problemi
            if (strpos($filename, 'temp_') !== false || strpos($filename, '\\') !== false || preg_match('/^[A-Z]:/', $filename)) {
                echo "<li style='color: red;'>❌ " . htmlspecialchars($filename) . " (PROBLEMA!)</li>";
            } else {
                echo "<li style='color: green;'>✅ " . htmlspecialchars($filename) . "</li>";
            }
        }
        echo "</ul>";
        $zip_read->close();
        
        // Link per download
        echo "<p><a href='download_backup.php?file=" . basename($zip_file) . "' target='_blank'>📥 Scarica ZIP di test</a></p>";
    }
    
} else {
    echo "<p>❌ Errore apertura ZIP</p>";
}

// Pulisci
if (file_exists($zip_file)) {
    // Non eliminare per permettere il download
    echo "<p>🔗 File ZIP mantenuto per test: " . htmlspecialchars(basename($zip_file)) . "</p>";
}

// Pulisci directory
unlink($backup_dir . DIRECTORY_SEPARATOR . 'backup_report.txt');
unlink($backup_dir . DIRECTORY_SEPARATOR . 'database' . DIRECTORY_SEPARATOR . 'test.sql');
unlink($backup_dir . DIRECTORY_SEPARATOR . 'files' . DIRECTORY_SEPARATOR . 'index.php');
rmdir($backup_dir . DIRECTORY_SEPARATOR . 'database');
rmdir($backup_dir . DIRECTORY_SEPARATOR . 'files');
rmdir($backup_dir);

echo "<p><a href='backup.php'>← Torna al backup</a></p>";
?>
