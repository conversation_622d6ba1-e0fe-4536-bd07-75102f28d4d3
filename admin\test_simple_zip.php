<?php
// admin/test_simple_zip.php
// Test semplice per verificare la struttura ZIP

session_start();
$_SESSION['user_id'] = 1;

echo "<h2>🔬 Test Semplice Struttura ZIP</h2>";

// Test molto semplice
$temp_dir = sys_get_temp_dir();
echo "<p><strong>Directory temporanea:</strong> " . htmlspecialchars($temp_dir) . "</p>";

// Crea una struttura di test molto semplice
$test_dir = $temp_dir . DIRECTORY_SEPARATOR . 'simple_test_' . time();
mkdir($test_dir);
mkdir($test_dir . DIRECTORY_SEPARATOR . 'database');
mkdir($test_dir . DIRECTORY_SEPARATOR . 'files');

// File di test
file_put_contents($test_dir . DIRECTORY_SEPARATOR . 'report.txt', 'Test Report');
file_put_contents($test_dir . DIRECTORY_SEPARATOR . 'database' . DIRECTORY_SEPARATOR . 'db.sql', 'CREATE TABLE test;');
file_put_contents($test_dir . DIRECTORY_SEPARATOR . 'files' . DIRECTORY_SEPARATOR . 'app.php', '<?php echo "test"; ?>');

echo "<p>✅ Struttura di test creata in: " . htmlspecialchars($test_dir) . "</p>";

// Crea ZIP con metodo diretto
$zip_file = $temp_dir . DIRECTORY_SEPARATOR . 'simple_test_' . time() . '.zip';
$zip = new ZipArchive();

if ($zip->open($zip_file, ZipArchive::CREATE) === TRUE) {
    
    // Metodo 1: Aggiungi file singolarmente con percorsi espliciti
    $zip->addFile($test_dir . DIRECTORY_SEPARATOR . 'report.txt', 'report.txt');
    $zip->addFile($test_dir . DIRECTORY_SEPARATOR . 'database' . DIRECTORY_SEPARATOR . 'db.sql', 'database/db.sql');
    $zip->addFile($test_dir . DIRECTORY_SEPARATOR . 'files' . DIRECTORY_SEPARATOR . 'app.php', 'files/app.php');
    
    $zip->close();
    
    echo "<p>✅ ZIP creato: " . htmlspecialchars(basename($zip_file)) . "</p>";
    
    // Verifica contenuto
    $zip_read = new ZipArchive();
    if ($zip_read->open($zip_file) === TRUE) {
        echo "<h3>📁 Contenuto ZIP:</h3><ul>";
        
        $clean_structure = true;
        for ($i = 0; $i < $zip_read->numFiles; $i++) {
            $file_info = $zip_read->statIndex($i);
            $filename = $file_info['name'];
            
            // Controlla se ci sono percorsi assoluti o cartelle annidate
            if (strpos($filename, ':') !== false || strpos($filename, 'temp_') !== false || strpos($filename, '\\') !== false) {
                $clean_structure = false;
                echo "<li style='color: red;'>❌ " . htmlspecialchars($filename) . " (PROBLEMA)</li>";
            } else {
                echo "<li style='color: green;'>✅ " . htmlspecialchars($filename) . "</li>";
            }
        }
        echo "</ul>";
        
        if ($clean_structure) {
            echo "<p style='color: green; font-weight: bold; font-size: 18px;'>🎉 SUCCESSO! Struttura ZIP pulita!</p>";
        } else {
            echo "<p style='color: red; font-weight: bold; font-size: 18px;'>❌ PROBLEMA! Struttura ZIP ancora sporca!</p>";
        }
        
        $zip_read->close();
    }
    
    // Pulisci
    unlink($zip_file);
} else {
    echo "<p style='color: red;'>❌ Errore creazione ZIP</p>";
}

// Pulisci directory test
unlink($test_dir . DIRECTORY_SEPARATOR . 'report.txt');
unlink($test_dir . DIRECTORY_SEPARATOR . 'database' . DIRECTORY_SEPARATOR . 'db.sql');
unlink($test_dir . DIRECTORY_SEPARATOR . 'files' . DIRECTORY_SEPARATOR . 'app.php');
rmdir($test_dir . DIRECTORY_SEPARATOR . 'database');
rmdir($test_dir . DIRECTORY_SEPARATOR . 'files');
rmdir($test_dir);

echo "<p>🧹 Test completato e pulito</p>";

echo "<hr>";
echo "<h3>🔧 Test con la logica del backup reale</h3>";

// Test con la logica esatta del backup
$backup_dir = sys_get_temp_dir() . DIRECTORY_SEPARATOR . 'backup_test_' . time();
mkdir($backup_dir, 0777, true);
mkdir($backup_dir . DIRECTORY_SEPARATOR . 'database');
mkdir($backup_dir . DIRECTORY_SEPARATOR . 'files');

file_put_contents($backup_dir . DIRECTORY_SEPARATOR . 'backup_report.txt', 'Real Test Report');
file_put_contents($backup_dir . DIRECTORY_SEPARATOR . 'database' . DIRECTORY_SEPARATOR . 'asdp.sql', 'CREATE TABLE users;');
file_put_contents($backup_dir . DIRECTORY_SEPARATOR . 'files' . DIRECTORY_SEPARATOR . 'index.php', '<?php echo "main"; ?>');

$zip_file2 = sys_get_temp_dir() . DIRECTORY_SEPARATOR . 'backup_test_' . time() . '.zip';
$zip2 = new ZipArchive();

if ($zip2->open($zip_file2, ZipArchive::CREATE) === TRUE) {
    
    // Usa la logica esatta del backup modificato
    $backup_dir_normalized = str_replace('\\', '/', $backup_dir);
    
    // Report
    $report_file = $backup_dir . DIRECTORY_SEPARATOR . 'backup_report.txt';
    if (file_exists($report_file)) {
        $zip2->addFile($report_file, 'backup_report.txt');
    }
    
    // Database
    $db_dir = $backup_dir . DIRECTORY_SEPARATOR . 'database';
    if (is_dir($db_dir)) {
        $source_dir_normalized = str_replace('\\', '/', $db_dir);
        $iterator = new RecursiveIteratorIterator(
            new RecursiveDirectoryIterator($db_dir, RecursiveDirectoryIterator::SKIP_DOTS),
            RecursiveIteratorIterator::SELF_FIRST
        );
        
        foreach ($iterator as $file) {
            if ($file->isFile()) {
                $file_path = $file->getRealPath();
                $file_path_normalized = str_replace('\\', '/', $file_path);
                $relative_from_source = substr($file_path_normalized, strlen($source_dir_normalized) + 1);
                $zip_path = 'database/' . $relative_from_source;
                $zip2->addFile($file_path, $zip_path);
            }
        }
    }
    
    // Files
    $files_dir = $backup_dir . DIRECTORY_SEPARATOR . 'files';
    if (is_dir($files_dir)) {
        $source_dir_normalized = str_replace('\\', '/', $files_dir);
        $iterator = new RecursiveIteratorIterator(
            new RecursiveDirectoryIterator($files_dir, RecursiveDirectoryIterator::SKIP_DOTS),
            RecursiveIteratorIterator::SELF_FIRST
        );
        
        foreach ($iterator as $file) {
            if ($file->isFile()) {
                $file_path = $file->getRealPath();
                $file_path_normalized = str_replace('\\', '/', $file_path);
                $relative_from_source = substr($file_path_normalized, strlen($source_dir_normalized) + 1);
                $zip_path = 'files/' . $relative_from_source;
                $zip2->addFile($file_path, $zip_path);
            }
        }
    }
    
    $zip2->close();
    
    // Verifica
    $zip_read2 = new ZipArchive();
    if ($zip_read2->open($zip_file2) === TRUE) {
        echo "<h4>📁 Contenuto ZIP (logica backup reale):</h4><ul>";
        
        $real_clean = true;
        for ($i = 0; $i < $zip_read2->numFiles; $i++) {
            $file_info = $zip_read2->statIndex($i);
            $filename = $file_info['name'];
            
            if (strpos($filename, ':') !== false || strpos($filename, 'temp_') !== false || strpos($filename, '\\') !== false) {
                $real_clean = false;
                echo "<li style='color: red;'>❌ " . htmlspecialchars($filename) . " (PROBLEMA)</li>";
            } else {
                echo "<li style='color: green;'>✅ " . htmlspecialchars($filename) . "</li>";
            }
        }
        echo "</ul>";
        
        if ($real_clean) {
            echo "<p style='color: green; font-weight: bold; font-size: 18px;'>🎉 BACKUP REALE: Struttura corretta!</p>";
            echo "<p><a href='backup.php' style='background: green; color: white; padding: 10px; text-decoration: none; border-radius: 5px;'>Prova il backup vero</a></p>";
        } else {
            echo "<p style='color: red; font-weight: bold; font-size: 18px;'>❌ BACKUP REALE: Serve ancora correzione!</p>";
        }
        
        $zip_read2->close();
    }
    
    unlink($zip_file2);
}

// Pulisci
unlink($backup_dir . DIRECTORY_SEPARATOR . 'backup_report.txt');
unlink($backup_dir . DIRECTORY_SEPARATOR . 'database' . DIRECTORY_SEPARATOR . 'asdp.sql');
unlink($backup_dir . DIRECTORY_SEPARATOR . 'files' . DIRECTORY_SEPARATOR . 'index.php');
rmdir($backup_dir . DIRECTORY_SEPARATOR . 'database');
rmdir($backup_dir . DIRECTORY_SEPARATOR . 'files');
rmdir($backup_dir);

echo "<p><a href='backup.php'>← Torna al backup</a></p>";
?>
