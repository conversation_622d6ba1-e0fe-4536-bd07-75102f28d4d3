<?php
// admin/test_backup_fix.php
// Test specifico per la correzione del backup ZIP

session_start();
$_SESSION['user_id'] = 1;
$_SESSION['nome'] = 'Test';
$_SESSION['cognome'] = 'Admin';

ini_set('display_errors', 1);
error_reporting(E_ALL);

echo "<h2>🔧 Test Correzione Backup ZIP</h2>";

// Simula la nuova logica di backup
function testNewBackupLogic() {
    echo "<h3>🧪 Test Nuova Logica Backup</h3>";
    
    // Crea directory temporanea di test
    $backup_dir = sys_get_temp_dir() . DIRECTORY_SEPARATOR . 'test_backup_' . date('Y-m-d_H-i-s');
    echo "<p><strong>Directory backup:</strong> " . htmlspecialchars($backup_dir) . "</p>";
    
    // Crea struttura di test
    mkdir($backup_dir, 0777, true);
    mkdir($backup_dir . DIRECTORY_SEPARATOR . 'database');
    mkdir($backup_dir . DIRECTORY_SEPARATOR . 'files');
    mkdir($backup_dir . DIRECTORY_SEPARATOR . 'files' . DIRECTORY_SEPARATOR . 'admin', 0777, true);
    mkdir($backup_dir . DIRECTORY_SEPARATOR . 'files' . DIRECTORY_SEPARATOR . 'includes', 0777, true);
    
    // Crea file di test
    file_put_contents($backup_dir . DIRECTORY_SEPARATOR . 'backup_report.txt', 'Test Report Content');
    file_put_contents($backup_dir . DIRECTORY_SEPARATOR . 'database' . DIRECTORY_SEPARATOR . 'asdp.sql', 'CREATE TABLE test (id INT);');
    file_put_contents($backup_dir . DIRECTORY_SEPARATOR . 'files' . DIRECTORY_SEPARATOR . 'index.php', '<?php echo "main"; ?>');
    file_put_contents($backup_dir . DIRECTORY_SEPARATOR . 'files' . DIRECTORY_SEPARATOR . 'admin' . DIRECTORY_SEPARATOR . 'dashboard.php', '<?php echo "admin"; ?>');
    file_put_contents($backup_dir . DIRECTORY_SEPARATOR . 'files' . DIRECTORY_SEPARATOR . 'includes' . DIRECTORY_SEPARATOR . 'config.php', '<?php define("TEST", true); ?>');
    
    echo "<p>✅ Struttura di test creata</p>";
    
    // Crea ZIP con la nuova logica
    $zip_file = sys_get_temp_dir() . DIRECTORY_SEPARATOR . 'test_backup_fix_' . date('Y-m-d_H-i-s') . '.zip';
    $zip = new ZipArchive();
    
    if ($zip->open($zip_file, ZipArchive::CREATE) !== TRUE) {
        echo "<p>❌ Errore creazione ZIP</p>";
        return false;
    }
    
    echo "<p>✅ ZIP aperto: " . htmlspecialchars(basename($zip_file)) . "</p>";
    
    // Normalizza il percorso della directory di backup per Windows
    $backup_dir_normalized = str_replace('\\', '/', $backup_dir);
    
    // Aggiungi il report nella root del ZIP
    $report_file = $backup_dir . DIRECTORY_SEPARATOR . 'backup_report.txt';
    if (file_exists($report_file)) {
        $zip->addFile($report_file, 'backup_report.txt');
        echo "<p>➕ Aggiunto: backup_report.txt</p>";
    }

    // Funzione helper per aggiungere file ricorsivamente
    $addDirectoryToZip = function($source_dir, $zip_prefix) use ($zip, $backup_dir_normalized) {
        if (!is_dir($source_dir)) {
            return;
        }
        
        $source_dir_normalized = str_replace('\\', '/', $source_dir);
        $iterator = new RecursiveIteratorIterator(
            new RecursiveDirectoryIterator($source_dir, RecursiveDirectoryIterator::SKIP_DOTS),
            RecursiveIteratorIterator::SELF_FIRST
        );
        
        foreach ($iterator as $file) {
            if ($file->isFile()) {
                $file_path = $file->getRealPath();
                $file_path_normalized = str_replace('\\', '/', $file_path);
                
                // Calcola il percorso relativo dal source_dir
                $relative_from_source = substr($file_path_normalized, strlen($source_dir_normalized) + 1);
                $zip_path = $zip_prefix . '/' . $relative_from_source;
                
                if ($zip->addFile($file_path, $zip_path)) {
                    echo "<p>➕ Aggiunto: " . htmlspecialchars($zip_path) . "</p>";
                } else {
                    echo "<p>❌ Errore aggiunta: " . htmlspecialchars($zip_path) . "</p>";
                }
            }
        }
    };

    // Aggiungi i file del database
    $db_dir = $backup_dir . DIRECTORY_SEPARATOR . 'database';
    echo "<h4>📊 Aggiunta file database:</h4>";
    $addDirectoryToZip($db_dir, 'database');

    // Aggiungi i file dell'applicazione
    $files_dir = $backup_dir . DIRECTORY_SEPARATOR . 'files';
    echo "<h4>📁 Aggiunta file applicazione:</h4>";
    $addDirectoryToZip($files_dir, 'files');
    
    $zip->close();
    echo "<p>✅ ZIP chiuso</p>";
    
    // Verifica contenuto ZIP
    echo "<h4>🔍 Verifica Contenuto ZIP:</h4>";
    $zip_read = new ZipArchive();
    if ($zip_read->open($zip_file) === TRUE) {
        echo "<ul>";
        $has_nested = false;
        $files_found = [];
        
        for ($i = 0; $i < $zip_read->numFiles; $i++) {
            $file_info = $zip_read->statIndex($i);
            $filename = $file_info['name'];
            $files_found[] = $filename;
            
            // Controlla se ci sono cartelle annidate indesiderate
            if (preg_match('/^[^\/]+\/temp_/', $filename) || preg_match('/^s\//', $filename) || preg_match('/^[A-Z]:/', $filename)) {
                $has_nested = true;
                echo "<li style='color: red;'>❌ " . htmlspecialchars($filename) . " (PROBLEMA!)</li>";
            } else {
                echo "<li style='color: green;'>✅ " . htmlspecialchars($filename) . "</li>";
            }
        }
        echo "</ul>";
        
        // Risultato finale
        if ($has_nested) {
            echo "<p style='color: red; font-weight: bold;'>❌ FALLITO: Trovate ancora cartelle annidate indesiderate!</p>";
        } else {
            echo "<p style='color: green; font-weight: bold;'>✅ SUCCESSO: Struttura ZIP corretta!</p>";
        }
        
        // Verifica file attesi
        $expected = ['backup_report.txt', 'database/asdp.sql', 'files/index.php', 'files/admin/dashboard.php', 'files/includes/config.php'];
        $missing = array_diff($expected, $files_found);
        
        if (empty($missing)) {
            echo "<p style='color: green;'>✅ Tutti i file attesi sono presenti</p>";
        } else {
            echo "<p style='color: orange;'>⚠️ File mancanti: " . implode(', ', $missing) . "</p>";
        }
        
        $zip_read->close();
    }
    
    // Pulisci
    unlink($zip_file);
    
    $iterator = new RecursiveIteratorIterator(
        new RecursiveDirectoryIterator($backup_dir, RecursiveDirectoryIterator::SKIP_DOTS),
        RecursiveIteratorIterator::CHILD_FIRST
    );
    
    foreach ($iterator as $file) {
        if ($file->isDir()) {
            rmdir($file->getRealPath());
        } else {
            unlink($file->getRealPath());
        }
    }
    rmdir($backup_dir);
    
    echo "<p>🧹 File di test puliti</p>";
    
    return !$has_nested;
}

// Esegui il test
$success = testNewBackupLogic();

echo "<hr>";
if ($success) {
    echo "<p style='color: green; font-size: 18px; font-weight: bold;'>🎉 TEST SUPERATO! La correzione funziona!</p>";
    echo "<p><a href='backup.php' style='background: green; color: white; padding: 10px; text-decoration: none; border-radius: 5px;'>Prova il backup reale</a></p>";
} else {
    echo "<p style='color: red; font-size: 18px; font-weight: bold;'>❌ TEST FALLITO! Serve ulteriore correzione.</p>";
}

echo "<p><a href='backup.php'>← Torna al pannello backup</a></p>";
?>
