<?php
// admin/backup_process_v2.php
// Versione alternativa del backup con approccio diretto

session_start();

// Abilita la visualizzazione degli errori
ini_set('display_errors', 1);
error_reporting(E_ALL);

// Verifica se l'utente è autenticato
if (!isset($_SESSION['user_id'])) {
    header('Content-Type: application/json');
    echo json_encode(['success' => false, 'error' => 'Utente non autenticato']);
    exit;
}

// Importa le dipendenze
require_once '../includes/db_config.php';
require_once '../includes/Logger.php';

$logger = Logger::getInstance();
$logger->info('Avvio processo di backup v2');

// Funzione per verificare se mysqldump è disponibile
function findMysqldump() {
    $paths = [
        'C:\\xampp\\mysql\\bin\\mysqldump.exe',
        'C:\\wamp64\\bin\\mysql\\mysql8.0.31\\bin\\mysqldump.exe',
        'C:\\wamp\\bin\\mysql\\mysql5.7.36\\bin\\mysqldump.exe'
    ];

    foreach ($paths as $path) {
        if (file_exists($path)) {
            return $path;
        }
    }

    return false;
}

// Funzione per il backup diretto
function performDirectBackup() {
    global $logger;
    
    try {
        $logger->info('Inizializzazione backup diretto');
        
        // Verifica mysqldump
        $mysqldump_path = findMysqldump();
        if (!$mysqldump_path) {
            throw new Exception("mysqldump non trovato");
        }

        // Crea il file ZIP direttamente
        $zip_file = sys_get_temp_dir() . DIRECTORY_SEPARATOR . 'asdp_backup_direct_' . date('Y-m-d_H-i-s') . '.zip';

        // Assicurati che la directory esista e sia scrivibile
        if (!is_writable(sys_get_temp_dir())) {
            throw new Exception("Directory temporanea non scrivibile: " . sys_get_temp_dir());
        }
        $zip = new ZipArchive();
        
        if ($zip->open($zip_file, ZipArchive::CREATE) !== TRUE) {
            throw new Exception("Impossibile creare il file ZIP: " . $zip_file);
        }

        $logger->info('ZIP aperto per scrittura diretta');

        // 1. Backup database diretto
        $temp_sql = sys_get_temp_dir() . DIRECTORY_SEPARATOR . 'temp_db_' . time() . '.sql';
        $command = sprintf(
            '"%s" --host=%s --user=%s --password=%s --databases %s > "%s" 2>&1',
            $mysqldump_path,
            escapeshellarg(DB_HOST),
            escapeshellarg(DB_USER),
            escapeshellarg(DB_PASS),
            escapeshellarg(DB_NAME),
            $temp_sql
        );

        exec($command, $output, $return_var);
        if ($return_var !== 0) {
            $logger->error('Errore mysqldump', ['output' => $output]);
            throw new Exception("Errore durante il backup del database: " . implode("\n", $output));
        }

        // Verifica che il file SQL sia stato creato
        if (!file_exists($temp_sql) || filesize($temp_sql) == 0) {
            throw new Exception("File SQL non creato o vuoto");
        }

        // Aggiungi il database al ZIP
        if (!$zip->addFile($temp_sql, 'database/' . DB_NAME . '.sql')) {
            throw new Exception("Impossibile aggiungere database al ZIP");
        }
        $logger->info('Database aggiunto al ZIP');

        // 2. Aggiungi file dell'applicazione direttamente
        $base_dir = dirname(__DIR__);
        $excluded_patterns = [
            'vendor/*', 'node_modules/*', '.git/*', 'temp/*', 'tmp/*', 'backups/*'
        ];

        $iterator = new RecursiveIteratorIterator(
            new RecursiveDirectoryIterator($base_dir),
            RecursiveIteratorIterator::SELF_FIRST
        );

        $files_added = 0;
        foreach ($iterator as $file) {
            if ($file->isFile()) {
                $file_path = $file->getRealPath();
                $relative_path = substr($file_path, strlen($base_dir) + 1);
                
                // Normalizza il percorso per Windows
                $relative_path = str_replace('\\', '/', $relative_path);
                
                // Controlla esclusioni
                $exclude = false;
                foreach ($excluded_patterns as $pattern) {
                    if (fnmatch($pattern, $relative_path)) {
                        $exclude = true;
                        break;
                    }
                }
                
                if (!$exclude) {
                    // Aggiungi direttamente al ZIP con prefisso 'files/'
                    $zip_path = 'files/' . $relative_path;
                    if ($zip->addFile($file_path, $zip_path)) {
                        $files_added++;
                    }
                }
            }
        }

        $logger->info('File applicazione aggiunti', ['count' => $files_added]);

        // 3. Genera e aggiungi report
        $report_content = "ASDP - Report Backup Diretto\n";
        $report_content .= "============================\n\n";
        $report_content .= "Data backup: " . date('d/m/Y H:i:s') . "\n";
        $report_content .= "Utente: " . $_SESSION['nome'] . " " . $_SESSION['cognome'] . "\n";
        $report_content .= "File aggiunti: " . $files_added . "\n";
        $report_content .= "Metodo: Backup diretto senza directory temporanee\n";

        $temp_report = sys_get_temp_dir() . DIRECTORY_SEPARATOR . 'temp_report_' . time() . '.txt';
        file_put_contents($temp_report, $report_content);
        $zip->addFile($temp_report, 'backup_report.txt');

        $zip->close();
        $logger->info('ZIP chiuso', ['size' => filesize($zip_file)]);

        // Pulisci file temporanei
        unlink($temp_sql);
        unlink($temp_report);

        return [
            'success' => true,
            'message' => 'Backup diretto completato con successo',
            'downloadUrl' => 'download_backup.php?file=' . basename($zip_file),
            'method' => 'direct'
        ];

    } catch (Exception $e) {
        $logger->error('Errore backup diretto', ['error' => $e->getMessage()]);
        throw $e;
    }
}

// Esegui il backup
try {
    header('Content-Type: application/json');
    $result = performDirectBackup();
    echo json_encode($result);
} catch (Exception $e) {
    header('Content-Type: application/json');
    echo json_encode([
        'success' => false,
        'error' => $e->getMessage()
    ]);
}
?>
