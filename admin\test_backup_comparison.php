<?php
// admin/test_backup_comparison.php
// Confronto tra metodo originale e metodo diretto

session_start();
$_SESSION['user_id'] = 1;
$_SESSION['nome'] = 'Test';
$_SESSION['cognome'] = 'User';

ini_set('display_errors', 1);
error_reporting(E_ALL);

echo "<h2>⚖️ Confronto Metodi Backup</h2>";
echo "<p>Questo test confronta il metodo originale con il metodo diretto per vedere quale produce una struttura ZIP più pulita.</p>";

?>
<style>
.method-test {
    border: 1px solid #ddd;
    margin: 20px 0;
    padding: 15px;
    border-radius: 5px;
}
.success { background-color: #d4edda; border-color: #c3e6cb; }
.error { background-color: #f8d7da; border-color: #f5c6cb; }
.warning { background-color: #fff3cd; border-color: #ffeaa7; }
</style>

<div class="method-test">
    <h3>🔧 Metodo 1: Backup Originale (Modificato)</h3>
    <p><strong>Descrizione:</strong> Usa directory temporanee intermedie, poi crea ZIP</p>
    
    <button onclick="testOriginalMethod()" style="background: #007bff; color: white; padding: 10px; border: none; border-radius: 5px; cursor: pointer;">
        Test Metodo Originale
    </button>
    
    <div id="original-result" style="margin-top: 10px;"></div>
</div>

<div class="method-test">
    <h3>🚀 Metodo 2: Backup Diretto</h3>
    <p><strong>Descrizione:</strong> Aggiunge file direttamente al ZIP senza directory temporanee</p>

    <button onclick="testDirectMethod()" style="background: #28a745; color: white; padding: 10px; border: none; border-radius: 5px; cursor: pointer;">
        Test Metodo Diretto
    </button>

    <div id="direct-result" style="margin-top: 10px;"></div>
</div>

<div class="method-test">
    <h3>🪟 Metodo 3: Windows Nativo</h3>
    <p><strong>Descrizione:</strong> Usa PowerShell di Windows per creare ZIP, con fallback a ZipArchive semplificato</p>

    <button onclick="testWindowsMethod()" style="background: #6f42c1; color: white; padding: 10px; border: none; border-radius: 5px; cursor: pointer;">
        Test Metodo Windows
    </button>

    <div id="windows-result" style="margin-top: 10px;"></div>
</div>

<div class="method-test">
    <h3>📊 Confronto Risultati</h3>
    <div id="comparison-result"></div>
</div>

<script>
async function testOriginalMethod() {
    const resultDiv = document.getElementById('original-result');
    resultDiv.innerHTML = '<p>🔄 Test in corso...</p>';
    
    try {
        const response = await fetch('backup_process.php', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            }
        });
        
        const result = await response.json();
        
        if (result.success) {
            resultDiv.innerHTML = `
                <div class="success">
                    <h4>✅ Metodo Originale - Successo</h4>
                    <p><strong>File:</strong> ${result.downloadUrl}</p>
                    <p><strong>Statistiche:</strong> ${result.stats ? result.stats.files_count : 'N/A'} file</p>
                    <p><a href="${result.downloadUrl}" target="_blank">📥 Scarica e verifica struttura</a></p>
                </div>
            `;
        } else {
            resultDiv.innerHTML = `
                <div class="error">
                    <h4>❌ Metodo Originale - Errore</h4>
                    <p>${result.error}</p>
                </div>
            `;
        }
    } catch (error) {
        resultDiv.innerHTML = `
            <div class="error">
                <h4>❌ Metodo Originale - Errore di rete</h4>
                <p>${error.message}</p>
            </div>
        `;
    }
}

async function testDirectMethod() {
    const resultDiv = document.getElementById('direct-result');
    resultDiv.innerHTML = '<p>🔄 Test in corso...</p>';

    try {
        const response = await fetch('backup_process_v2.php', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            }
        });

        const result = await response.json();

        if (result.success) {
            resultDiv.innerHTML = `
                <div class="success">
                    <h4>✅ Metodo Diretto - Successo</h4>
                    <p><strong>File:</strong> ${result.downloadUrl}</p>
                    <p><strong>Metodo:</strong> ${result.method}</p>
                    <p><a href="${result.downloadUrl}" target="_blank">📥 Scarica e verifica struttura</a></p>
                </div>
            `;
        } else {
            resultDiv.innerHTML = `
                <div class="error">
                    <h4>❌ Metodo Diretto - Errore</h4>
                    <p>${result.error}</p>
                </div>
            `;
        }
    } catch (error) {
        resultDiv.innerHTML = `
            <div class="error">
                <h4>❌ Metodo Diretto - Errore di rete</h4>
                <p>${error.message}</p>
            </div>
        `;
    }
}

async function testWindowsMethod() {
    const resultDiv = document.getElementById('windows-result');
    resultDiv.innerHTML = '<p>🔄 Test in corso...</p>';

    try {
        const response = await fetch('backup_process_v3.php', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            }
        });

        const result = await response.json();

        if (result.success) {
            resultDiv.innerHTML = `
                <div class="success">
                    <h4>✅ Metodo Windows - Successo</h4>
                    <p><strong>File:</strong> ${result.downloadUrl}</p>
                    <p><strong>Metodo:</strong> ${result.method}</p>
                    <p><strong>File copiati:</strong> ${result.files_copied}</p>
                    <p><a href="${result.downloadUrl}" target="_blank">📥 Scarica e verifica struttura</a></p>
                </div>
            `;
        } else {
            resultDiv.innerHTML = `
                <div class="error">
                    <h4>❌ Metodo Windows - Errore</h4>
                    <p>${result.error}</p>
                </div>
            `;
        }
    } catch (error) {
        resultDiv.innerHTML = `
            <div class="error">
                <h4>❌ Metodo Windows - Errore di rete</h4>
                <p>${error.message}</p>
            </div>
        `;
    }
}

function updateComparison() {
    const comparisonDiv = document.getElementById('comparison-result');
    const originalDiv = document.getElementById('original-result');
    const directDiv = document.getElementById('direct-result');
    const windowsDiv = document.getElementById('windows-result');

    const originalSuccess = originalDiv.innerHTML.includes('✅');
    const directSuccess = directDiv.innerHTML.includes('✅');
    const windowsSuccess = windowsDiv.innerHTML.includes('✅');

    const successCount = [originalSuccess, directSuccess, windowsSuccess].filter(Boolean).length;

    if (successCount === 3) {
        comparisonDiv.innerHTML = `
            <div class="success">
                <h4>🎉 Tutti e tre i metodi funzionano!</h4>
                <p>Scarica tutti i file ZIP e confronta le strutture per vedere quale è più pulita.</p>
                <p><strong>Raccomandazione:</strong> Usa il metodo che produce la struttura più pulita.</p>
            </div>
        `;
    } else if (successCount === 2) {
        const working = [];
        if (originalSuccess) working.push('Originale');
        if (directSuccess) working.push('Diretto');
        if (windowsSuccess) working.push('Windows');

        comparisonDiv.innerHTML = `
            <div class="success">
                <h4>✅ Due metodi funzionano: ${working.join(' e ')}</h4>
                <p>Confronta le strutture ZIP per scegliere il migliore.</p>
            </div>
        `;
    } else if (successCount === 1) {
        const working = originalSuccess ? 'Originale' : (directSuccess ? 'Diretto' : 'Windows');
        comparisonDiv.innerHTML = `
            <div class="warning">
                <h4>⚠️ Solo il metodo ${working} funziona</h4>
                <p>Usa questo metodo e verifica la struttura ZIP.</p>
            </div>
        `;
    } else {
        comparisonDiv.innerHTML = `
            <div class="error">
                <h4>❌ Tutti i metodi hanno problemi</h4>
                <p>Controlla il debug approfondito per identificare il problema di base.</p>
                <p><a href="debug_deep_backup.php" target="_blank">🔬 Vai al debug approfondito</a></p>
            </div>
        `;
    }
}

// Aggiorna il confronto ogni 2 secondi
setInterval(updateComparison, 2000);
</script>

<hr>
<h3>📋 Istruzioni per il Test</h3>
<ol>
    <li>Clicca su "Test Metodo Originale" per testare il backup modificato</li>
    <li>Clicca su "Test Metodo Diretto" per testare il nuovo approccio</li>
    <li>Scarica entrambi i file ZIP generati</li>
    <li>Apri i file ZIP e confronta le strutture:
        <ul>
            <li>✅ <strong>Struttura corretta:</strong> <code>backup.zip\database\files\backup_report.txt</code></li>
            <li>❌ <strong>Struttura sbagliata:</strong> <code>backup.zip\s\temp_xxx\database\files\</code></li>
        </ul>
    </li>
    <li>Scegli il metodo che produce la struttura più pulita</li>
</ol>

<p><a href="backup.php">← Torna al pannello backup</a></p>
