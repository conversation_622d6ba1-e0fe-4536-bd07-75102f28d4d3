<?php
// admin/debug_backup_paths.php
// Debug per capire i percorsi del backup

session_start();
$_SESSION['user_id'] = 1; // Simula autenticazione

echo "<h2>🔍 Debug Percorsi Backup</h2>";

// Test dei percorsi
$temp_dir = sys_get_temp_dir();
$backup_dir = $temp_dir . '/asdp_backup_' . date('Y-m-d_H-i-s');

echo "<h3>📁 Informazioni Percorsi</h3>";
echo "<p><strong>sys_get_temp_dir():</strong> " . htmlspecialchars($temp_dir) . "</p>";
echo "<p><strong>backup_dir:</strong> " . htmlspecialchars($backup_dir) . "</p>";
echo "<p><strong>backup_dir normalizzato:</strong> " . htmlspecialchars(str_replace('\\', '/', $backup_dir)) . "</p>";

// Crea directory di test
if (!is_dir($backup_dir)) {
    mkdir($backup_dir, 0777, true);
    mkdir($backup_dir . '/database');
    mkdir($backup_dir . '/files');
    mkdir($backup_dir . '/files/admin', 0777, true);
}

// Crea file di test
file_put_contents($backup_dir . '/backup_report.txt', 'Test report');
file_put_contents($backup_dir . '/database/test.sql', 'CREATE TABLE test (id INT);');
file_put_contents($backup_dir . '/files/index.php', '<?php echo "test"; ?>');
file_put_contents($backup_dir . '/files/admin/test.php', '<?php echo "admin"; ?>');

echo "<h3>📂 Test Percorsi Relativi</h3>";

// Test per il report
$report_file = $backup_dir . '/backup_report.txt';
echo "<p><strong>Report file:</strong> " . htmlspecialchars($report_file) . "</p>";

// Test per database
$db_dir = $backup_dir . '/database';
$db_files = new RecursiveIteratorIterator(
    new RecursiveDirectoryIterator($db_dir),
    RecursiveIteratorIterator::SELF_FIRST
);

echo "<h4>🗄️ File Database:</h4>";
foreach ($db_files as $file) {
    if ($file->isFile()) {
        $file_path = $file->getRealPath();
        $relative_path_old = substr($file_path, strlen($db_dir) + 1);
        $relative_path_new = 'database/' . str_replace('\\', '/', substr($file_path, strlen($db_dir) + 1));
        
        echo "<p>";
        echo "<strong>File:</strong> " . htmlspecialchars($file_path) . "<br>";
        echo "<strong>Relativo vecchio:</strong> " . htmlspecialchars($relative_path_old) . "<br>";
        echo "<strong>Relativo nuovo:</strong> " . htmlspecialchars($relative_path_new) . "<br>";
        echo "</p>";
    }
}

// Test per files
$files_dir = $backup_dir . '/files';
$app_files = new RecursiveIteratorIterator(
    new RecursiveDirectoryIterator($files_dir),
    RecursiveIteratorIterator::SELF_FIRST
);

echo "<h4>📄 File Applicazione:</h4>";
foreach ($app_files as $file) {
    if ($file->isFile()) {
        $file_path = $file->getRealPath();
        $relative_path_old = substr($file_path, strlen($files_dir) + 1);
        $relative_path_new = 'files/' . str_replace('\\', '/', substr($file_path, strlen($files_dir) + 1));
        
        echo "<p>";
        echo "<strong>File:</strong> " . htmlspecialchars($file_path) . "<br>";
        echo "<strong>Relativo vecchio:</strong> " . htmlspecialchars($relative_path_old) . "<br>";
        echo "<strong>Relativo nuovo:</strong> " . htmlspecialchars($relative_path_new) . "<br>";
        echo "</p>";
    }
}

// Test creazione ZIP
echo "<h3>📦 Test Creazione ZIP</h3>";

$zip_file = $temp_dir . '/debug_backup_' . date('Y-m-d_H-i-s') . '.zip';
$zip = new ZipArchive();

if ($zip->open($zip_file, ZipArchive::CREATE) === TRUE) {
    echo "<p>✅ ZIP aperto correttamente: " . htmlspecialchars($zip_file) . "</p>";
    
    // Aggiungi report
    $zip->addFile($backup_dir . '/backup_report.txt', 'backup_report.txt');
    echo "<p>➕ Aggiunto: backup_report.txt</p>";
    
    // Aggiungi database files
    $db_files = new RecursiveIteratorIterator(
        new RecursiveDirectoryIterator($backup_dir . '/database'),
        RecursiveIteratorIterator::SELF_FIRST
    );
    
    foreach ($db_files as $file) {
        if ($file->isFile()) {
            $file_path = $file->getRealPath();
            $relative_path = 'database/' . str_replace('\\', '/', substr($file_path, strlen($backup_dir . '/database') + 1));
            $zip->addFile($file_path, $relative_path);
            echo "<p>➕ Aggiunto DB: " . htmlspecialchars($relative_path) . "</p>";
        }
    }
    
    // Aggiungi application files
    $app_files = new RecursiveIteratorIterator(
        new RecursiveDirectoryIterator($backup_dir . '/files'),
        RecursiveIteratorIterator::SELF_FIRST
    );
    
    foreach ($app_files as $file) {
        if ($file->isFile()) {
            $file_path = $file->getRealPath();
            $relative_path = 'files/' . str_replace('\\', '/', substr($file_path, strlen($backup_dir . '/files') + 1));
            $zip->addFile($file_path, $relative_path);
            echo "<p>➕ Aggiunto APP: " . htmlspecialchars($relative_path) . "</p>";
        }
    }
    
    $zip->close();
    echo "<p>✅ ZIP chiuso</p>";
    
    // Verifica contenuto ZIP
    echo "<h4>🔍 Contenuto ZIP:</h4>";
    $zip_read = new ZipArchive();
    if ($zip_read->open($zip_file) === TRUE) {
        echo "<ul>";
        for ($i = 0; $i < $zip_read->numFiles; $i++) {
            $file_info = $zip_read->statIndex($i);
            echo "<li>" . htmlspecialchars($file_info['name']) . "</li>";
        }
        echo "</ul>";
        $zip_read->close();
    }
    
    // Pulisci
    unlink($zip_file);
} else {
    echo "<p>❌ Errore apertura ZIP</p>";
}

// Pulisci directory di test
$iterator = new RecursiveIteratorIterator(
    new RecursiveDirectoryIterator($backup_dir, RecursiveDirectoryIterator::SKIP_DOTS),
    RecursiveIteratorIterator::CHILD_FIRST
);

foreach ($iterator as $file) {
    if ($file->isDir()) {
        rmdir($file->getRealPath());
    } else {
        unlink($file->getRealPath());
    }
}
rmdir($backup_dir);

echo "<p>🧹 Directory di test pulita</p>";
echo "<hr>";
echo "<p><a href='backup.php'>← Torna al backup</a></p>";
?>
