<?php
// admin/test_zip_minimal.php
// Test minimale per identificare il problema ZIP

session_start();
$_SESSION['user_id'] = 1;

ini_set('display_errors', 1);
error_reporting(E_ALL);

echo "<h2>🔬 Test ZIP Minimale</h2>";

// Test 1: ZIP completamente vuoto
echo "<h3>Test 1: ZIP Vuoto</h3>";
$zip1 = sys_get_temp_dir() . DIRECTORY_SEPARATOR . 'test_empty_' . time() . '.zip';
$zip = new ZipArchive();

if ($zip->open($zip1, ZipArchive::CREATE) === TRUE) {
    $zip->close();
    echo "<p>✅ ZIP vuoto creato: " . htmlspecialchars(basename($zip1)) . "</p>";
    echo "<p>Dimensione: " . filesize($zip1) . " bytes</p>";
    echo "<p><a href='download_backup.php?file=" . basename($zip1) . "' target='_blank'>📥 Test ZIP vuoto</a></p>";
} else {
    echo "<p>❌ Errore creazione ZIP vuoto</p>";
}

// Test 2: ZIP con un solo file da stringa
echo "<h3>Test 2: ZIP con File da Stringa</h3>";
$zip2 = sys_get_temp_dir() . DIRECTORY_SEPARATOR . 'test_string_' . time() . '.zip';
$zip = new ZipArchive();

if ($zip->open($zip2, ZipArchive::CREATE) === TRUE) {
    $zip->addFromString('test.txt', 'Contenuto di test');
    $zip->close();
    echo "<p>✅ ZIP con stringa creato: " . htmlspecialchars(basename($zip2)) . "</p>";
    echo "<p>Dimensione: " . filesize($zip2) . " bytes</p>";
    echo "<p><a href='download_backup.php?file=" . basename($zip2) . "' target='_blank'>📥 Test ZIP stringa</a></p>";
} else {
    echo "<p>❌ Errore creazione ZIP con stringa</p>";
}

// Test 3: ZIP con file fisico
echo "<h3>Test 3: ZIP con File Fisico</h3>";
$temp_file = sys_get_temp_dir() . DIRECTORY_SEPARATOR . 'temp_test_' . time() . '.txt';
file_put_contents($temp_file, 'Contenuto file fisico');

$zip3 = sys_get_temp_dir() . DIRECTORY_SEPARATOR . 'test_file_' . time() . '.zip';
$zip = new ZipArchive();

if ($zip->open($zip3, ZipArchive::CREATE) === TRUE) {
    $result = $zip->addFile($temp_file, 'test_fisico.txt');
    echo "<p>Aggiunta file: " . ($result ? '✅ Successo' : '❌ Fallito') . "</p>";
    $zip->close();
    echo "<p>✅ ZIP con file fisico creato: " . htmlspecialchars(basename($zip3)) . "</p>";
    echo "<p>Dimensione: " . filesize($zip3) . " bytes</p>";
    echo "<p><a href='download_backup.php?file=" . basename($zip3) . "' target='_blank'>📥 Test ZIP file fisico</a></p>";
} else {
    echo "<p>❌ Errore creazione ZIP con file fisico</p>";
}

// Test 4: Simula la struttura del backup
echo "<h3>Test 4: Struttura Backup Semplificata</h3>";
$backup_test_dir = sys_get_temp_dir() . DIRECTORY_SEPARATOR . 'backup_test_' . time();
mkdir($backup_test_dir);
mkdir($backup_test_dir . DIRECTORY_SEPARATOR . 'database');
mkdir($backup_test_dir . DIRECTORY_SEPARATOR . 'files');

file_put_contents($backup_test_dir . DIRECTORY_SEPARATOR . 'report.txt', 'Report di test');
file_put_contents($backup_test_dir . DIRECTORY_SEPARATOR . 'database' . DIRECTORY_SEPARATOR . 'db.sql', 'CREATE TABLE test;');
file_put_contents($backup_test_dir . DIRECTORY_SEPARATOR . 'files' . DIRECTORY_SEPARATOR . 'app.php', '<?php echo "test"; ?>');

$zip4 = sys_get_temp_dir() . DIRECTORY_SEPARATOR . 'test_backup_' . time() . '.zip';
$zip = new ZipArchive();

if ($zip->open($zip4, ZipArchive::CREATE) === TRUE) {
    echo "<p>ZIP backup test aperto</p>";
    
    // Metodo 1: Aggiungi file singolarmente con percorsi espliciti
    $report_result = $zip->addFile($backup_test_dir . DIRECTORY_SEPARATOR . 'report.txt', 'report.txt');
    echo "<p>Report: " . ($report_result ? '✅' : '❌') . "</p>";
    
    $db_result = $zip->addFile($backup_test_dir . DIRECTORY_SEPARATOR . 'database' . DIRECTORY_SEPARATOR . 'db.sql', 'database/db.sql');
    echo "<p>Database: " . ($db_result ? '✅' : '❌') . "</p>";
    
    $app_result = $zip->addFile($backup_test_dir . DIRECTORY_SEPARATOR . 'files' . DIRECTORY_SEPARATOR . 'app.php', 'files/app.php');
    echo "<p>App file: " . ($app_result ? '✅' : '❌') . "</p>";
    
    $zip->close();
    echo "<p>✅ ZIP backup test creato: " . htmlspecialchars(basename($zip4)) . "</p>";
    echo "<p>Dimensione: " . filesize($zip4) . " bytes</p>";
    echo "<p><a href='download_backup.php?file=" . basename($zip4) . "' target='_blank'>📥 Test ZIP backup</a></p>";
} else {
    echo "<p>❌ Errore creazione ZIP backup test</p>";
}

// Pulisci file temporanei (ma non i ZIP per il test)
unlink($temp_file);
unlink($backup_test_dir . DIRECTORY_SEPARATOR . 'report.txt');
unlink($backup_test_dir . DIRECTORY_SEPARATOR . 'database' . DIRECTORY_SEPARATOR . 'db.sql');
unlink($backup_test_dir . DIRECTORY_SEPARATOR . 'files' . DIRECTORY_SEPARATOR . 'app.php');
rmdir($backup_test_dir . DIRECTORY_SEPARATOR . 'database');
rmdir($backup_test_dir . DIRECTORY_SEPARATOR . 'files');
rmdir($backup_test_dir);

echo "<hr>";
echo "<h3>📋 Istruzioni</h3>";
echo "<p>Testa tutti i link di download sopra per vedere quale funziona e quale no.</p>";
echo "<p>Questo ci aiuterà a capire esattamente dove è il problema.</p>";

echo "<p><a href='backup.php'>← Torna al backup</a></p>";
?>
