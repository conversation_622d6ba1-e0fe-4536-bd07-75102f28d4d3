<?php
// admin/test_backup_structure.php
// Test per verificare la struttura corretta del backup ZIP

session_start();

// Simula autenticazione admin per il test
$_SESSION['user_id'] = 1;
$_SESSION['nome'] = 'Test';
$_SESSION['cognome'] = 'Admin';

// Abilita la visualizzazione degli errori
ini_set('display_errors', 1);
error_reporting(E_ALL);

echo "<h2>Test Struttura Backup ZIP</h2>";
echo "<p>Questo test verifica che il backup ZIP abbia la struttura corretta senza cartelle annidate indesiderate.</p>";

// Importa le dipendenze
require_once '../includes/db_config.php';
require_once '../includes/Logger.php';

$logger = Logger::getInstance();

// Funzione per testare la struttura del ZIP
function testZipStructure() {
    global $logger;
    
    try {
        echo "<h3>🔍 Avvio test backup...</h3>";
        
        // Crea una directory temporanea di test
        $test_dir = sys_get_temp_dir() . '/test_backup_' . date('Y-m-d_H-i-s');
        mkdir($test_dir, 0777, true);
        mkdir($test_dir . '/database');
        mkdir($test_dir . '/files');
        
        // Crea file di test
        file_put_contents($test_dir . '/backup_report.txt', 'Test report');
        file_put_contents($test_dir . '/database/test.sql', 'CREATE TABLE test (id INT);');
        
        // Crea struttura file di test
        mkdir($test_dir . '/files/admin', 0777, true);
        mkdir($test_dir . '/files/includes', 0777, true);
        file_put_contents($test_dir . '/files/index.php', '<?php echo "test"; ?>');
        file_put_contents($test_dir . '/files/admin/test.php', '<?php echo "admin test"; ?>');
        file_put_contents($test_dir . '/files/includes/config.php', '<?php define("TEST", true); ?>');
        
        echo "<p>✅ Directory di test creata: " . $test_dir . "</p>";
        
        // Crea il ZIP con la nuova logica
        $zip_file = sys_get_temp_dir() . '/test_backup_' . date('Y-m-d_H-i-s') . '.zip';
        $zip = new ZipArchive();
        
        if ($zip->open($zip_file, ZipArchive::CREATE) !== TRUE) {
            throw new Exception("Impossibile creare il file ZIP di test");
        }
        
        // Aggiungi il report nella root del ZIP
        $zip->addFile($test_dir . '/backup_report.txt', 'backup_report.txt');
        
        // Aggiungi i file del database
        $db_files = new RecursiveIteratorIterator(
            new RecursiveDirectoryIterator($test_dir . '/database'),
            RecursiveIteratorIterator::SELF_FIRST
        );
        
        foreach ($db_files as $file) {
            if ($file->isFile()) {
                $file_path = $file->getRealPath();
                $relative_path = 'database/' . str_replace('\\', '/', substr($file_path, strlen($test_dir . '/database') + 1));
                $zip->addFile($file_path, $relative_path);
            }
        }
        
        // Aggiungi i file dell'applicazione
        $app_files = new RecursiveIteratorIterator(
            new RecursiveDirectoryIterator($test_dir . '/files'),
            RecursiveIteratorIterator::SELF_FIRST
        );
        
        foreach ($app_files as $file) {
            if ($file->isFile()) {
                $file_path = $file->getRealPath();
                $relative_path = 'files/' . str_replace('\\', '/', substr($file_path, strlen($test_dir . '/files') + 1));
                $zip->addFile($file_path, $relative_path);
            }
        }
        
        $zip->close();
        
        echo "<p>✅ File ZIP di test creato: " . basename($zip_file) . "</p>";
        
        // Verifica la struttura del ZIP
        $zip_read = new ZipArchive();
        if ($zip_read->open($zip_file) === TRUE) {
            echo "<h3>📁 Struttura del ZIP:</h3>";
            echo "<ul>";
            
            $expected_files = [
                'backup_report.txt',
                'database/test.sql',
                'files/index.php',
                'files/admin/test.php',
                'files/includes/config.php'
            ];
            
            $found_files = [];
            
            for ($i = 0; $i < $zip_read->numFiles; $i++) {
                $file_info = $zip_read->statIndex($i);
                $filename = $file_info['name'];
                $found_files[] = $filename;
                echo "<li>" . htmlspecialchars($filename) . "</li>";
            }
            
            echo "</ul>";
            
            // Verifica che non ci siano cartelle annidate indesiderate
            $has_nested_folders = false;
            foreach ($found_files as $file) {
                if (preg_match('/^[^\/]+\/temp_/', $file) || preg_match('/^s\//', $file)) {
                    $has_nested_folders = true;
                    break;
                }
            }
            
            if ($has_nested_folders) {
                echo "<p>❌ <strong>ERRORE:</strong> Trovate cartelle annidate indesiderate!</p>";
            } else {
                echo "<p>✅ <strong>SUCCESSO:</strong> Struttura ZIP corretta, nessuna cartella annidata indesiderata!</p>";
            }
            
            // Verifica che tutti i file attesi siano presenti
            $missing_files = array_diff($expected_files, $found_files);
            if (empty($missing_files)) {
                echo "<p>✅ <strong>SUCCESSO:</strong> Tutti i file attesi sono presenti nel ZIP!</p>";
            } else {
                echo "<p>❌ <strong>ERRORE:</strong> File mancanti: " . implode(', ', $missing_files) . "</p>";
            }
            
            $zip_read->close();
        }
        
        // Pulisci i file di test
        unlink($zip_file);
        
        $iterator = new RecursiveIteratorIterator(
            new RecursiveDirectoryIterator($test_dir, RecursiveDirectoryIterator::SKIP_DOTS),
            RecursiveIteratorIterator::CHILD_FIRST
        );
        
        foreach ($iterator as $file) {
            if ($file->isDir()) {
                rmdir($file->getRealPath());
            } else {
                unlink($file->getRealPath());
            }
        }
        rmdir($test_dir);
        
        echo "<p>✅ File di test puliti</p>";
        
    } catch (Exception $e) {
        echo "<p>❌ <strong>ERRORE:</strong> " . htmlspecialchars($e->getMessage()) . "</p>";
    }
}

// Esegui il test
testZipStructure();

echo "<hr>";
echo "<p><a href='backup.php'>← Torna al pannello backup</a></p>";
?>
